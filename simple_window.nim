import nigui

# Initialize the GUI application
app.init()

# Create a window
var window = newWindow("Simple Nim Window")
window.width = 400
window.height = 300

# Create a container for layout
var container = newLayoutContainer(Layout_Vertical)
window.add(container)

# Add a label
var label = new<PERSON>abel("Hello from Nim!")
label.fontSize = 16
container.add(label)

# Add a button
var button = newButton("Click Me!")
button.onClick = proc(event: ClickEvent) =
  label.text = "Button was clicked!"

container.add(button)

# Show the window and run the application
window.show()
app.run()
