# Simple Nim Window

A basic GUI application written in Nim using the `nigui` library.

## Features

- Simple window with a title "Simple Nim Window"
- A label displaying "Hello from Nim!"
- A clickable button that changes the label text when clicked
- Cross-platform GUI (Windows, macOS, Linux)

## Requirements

- Nim compiler (version 1.6.0 or higher)
- nigui library (automatically installed)

## How to Run

1. Compile and run the application:
   ```bash
   nim c -r simple_window.nim
   ```

2. Or compile for release (optimized):
   ```bash
   nim c -d:release simple_window.nim
   ./simple_window
   ```

## Files

- `simple_window.nim` - Main application source code
- `simple_window.nimble` - Package configuration file
- `README.md` - This documentation file

## About the Code

The application demonstrates basic GUI concepts in Nim:
- Window creation and configuration
- Layout containers for organizing UI elements
- Event handling (button clicks)
- Dynamic text updates

The `nigui` library provides a simple, cross-platform way to create desktop applications in Nim without requiring complex setup or large dependencies.
